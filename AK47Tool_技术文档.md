# AK47Tool Web3工具集 - 完整技术文档

## 项目概述

AK47Tool是一个基于Vue.js的Web3工具集，提供钱包生成、批量分发和合约交互功能。该工具专为以太坊及兼容链设计，支持多种网络和自定义RPC端点。

### 核心功能模块
1. **钱包生成器** - 支持助记词和随机生成模式
2. **批量分发** - 通过Disperse合约进行批量ETH分发
3. **合约交互** - 支持自定义合约交互和归集功能

---

## 系统架构

### 技术栈
- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **Web3库**: ethers.js v6 + web3.js v4
- **钱包库**: @ethereumjs/wallet, bip39
- **样式**: 原生CSS (响应式设计)
- **状态管理**: Vue 3 响应式系统 + localStorage持久化

### 项目结构
```
ak47tool/
├── src/
│   ├── App.vue                    # 主应用组件(Tab导航)
│   ├── main.js                    # 应用入口
│   ├── components/
│   │   ├── WalletGenerator.vue    # 钱包生成器
│   │   ├── BatchDisperse.vue      # 批量分发
│   │   ├── ContractInteraction.vue # 合约交互
│   │   ├── common/                # 通用组件
│   │   ├── layout/                # 布局组件
│   │   ├── network/               # 网络相关组件
│   │   └── wallet/                # 钱包相关组件
│   ├── composables/               # 组合式函数
│   │   ├── useWalletGeneration.js # 钱包生成逻辑
│   │   ├── useWeb3.js            # Web3交互逻辑
│   │   ├── useFileHandler.js     # 文件处理逻辑
│   │   └── useStorage.js         # 本地存储逻辑
│   ├── utils/                     # 工具函数
│   │   ├── constants.js          # 常量配置
│   │   ├── formatters.js         # 格式化函数
│   │   ├── helpers.js            # 辅助函数
│   │   └── validators.js         # 验证函数
│   └── assets/                    # 样式资源
├── public/                        # 静态资源
├── package.json                   # 项目配置
└── vite.config.js                # 构建配置
```

### 设计模式
1. **组合式API模式**: 使用Vue 3 Composition API进行逻辑组织
2. **可组合函数模式**: 业务逻辑封装在composables中实现复用
3. **事件驱动模式**: 组件间通过事件进行通信
4. **持久化模式**: 自动保存用户配置和数据到localStorage

---

## 功能模块详细分析

### 1. 钱包生成器 (WalletGenerator)

#### 功能特性
- **双模式生成**:
  - 助记词模式: 基于单个助记词生成多个HD钱包
  - 随机模式: 每个钱包独立随机生成
- **批量生成**: 支持1-10000个钱包批量生成
- **金额配置**: 可选择是否包含ETH数量
- **数据导出**: 支持多种格式文件下载
- **数据持久化**: 自动保存生成的钱包数据

#### 技术实现

**核心组件**: `src/components/WalletGenerator.vue`
**核心逻辑**: `src/composables/useWalletGeneration.js`

**助记词生成算法**:
```javascript
// 使用BIP44标准派生路径: m/44'/60'/0'/0/{index}
const mnemonic = ethers.Wallet.createRandom().mnemonic.phrase
for (let i = 0; i < numWallets; i++) {
  const derivationPath = `m/44'/60'/0'/0/${i}`
  const hdNode = ethers.HDNodeWallet.fromPhrase(mnemonic, undefined, derivationPath)
  // 提取地址和私钥
}
```

**随机生成算法**:
```javascript
// 确保生成不重复的钱包地址
const generatedAddresses = new Set()
for (let i = 0; i < numWallets; i++) {
  let wallet
  do {
    wallet = ethers.Wallet.createRandom()
  } while (generatedAddresses.has(wallet.address))
  // 存储钱包信息
}
```

**数据结构**:
```javascript
const generatedData = {
  addresses: [],           // 地址数组
  privateKeys: [{          // 私钥对象数组
    address: '0x...',
    privateKey: '0x...'
  }],
  mnemonic: 'word1 word2...' // 助记词(仅助记词模式)
}
```

**文件导出格式**:
- `address.txt`: 地址列表(可选包含金额)
- `address-w.txt`: 地址+私钥格式
- `mnemonic.txt`: 助记词文件

### 2. 批量分发 (BatchDisperse)

#### 功能特性
- **批量ETH分发**: 一次交易向多个地址发送不同数量的ETH
- **Gas价格监控**: 自动等待Gas价格降到可接受范围
- **智能预估**: 预估Gas费用和总成本
- **实时状态**: 显示执行进度和交易状态
- **Disperse合约**: 使用第三方Disperse合约实现批量分发

#### 技术实现

**核心组件**: `src/components/BatchDisperse.vue`
**合约地址**: `******************************************`

**Disperse合约ABI**:
```javascript
const DISPERSE_ABI = [{
  "constant": false,
  "inputs": [
    {"name": "recipients", "type": "address[]"},
    {"name": "values", "type": "uint256[]"}
  ],
  "name": "disperseEther", 
  "outputs": [],
  "payable": true,
  "stateMutability": "payable",
  "type": "function"
}]
```

**批量分发流程**:
1. **文件解析**: 解析`地址,金额`格式的文件
2. **余额检查**: 验证发送者余额是否充足
3. **Gas预估**: 预估交易所需Gas费用
4. **Gas监控**: 等待Gas价格降到用户设定范围
5. **交易执行**: 调用Disperse合约执行批量分发
6. **状态更新**: 实时更新执行状态和结果

**核心算法**:
```javascript
// 数据准备
const addresses = addressData.map(item => item.address)
const amounts = addressData.map(item => 
  web3.utils.toWei(item.amount.toString(), 'ether')
)

// 合约调用
const contract = new web3.eth.Contract(DISPERSE_ABI, contractAddress)
const txData = contract.methods.disperseEther(addresses, amounts).encodeABI()

// 交易构建
const txObject = {
  from: senderAddress,
  to: contractAddress,
  value: totalAmountWei.toString(),
  data: txData,
  gasPrice: finalGasPrice,
  gas: gasLimit
}
```

**Gas管理策略**:
- 用户设定最大Gas价格
- 自动添加额外Gas避免交易失败
- 实时监控当前网络Gas价格
- 智能等待机制，价格合适时自动执行

### 3. 合约交互 (ContractInteraction)

#### 功能特性
- **批量交易**: 支持多个钱包同时与合约交互
- **归集模式**: 自动计算最大可转金额(余额-Gas费)
- **自定义交互**: 支持任意合约地址和calldata
- **Gas优化**: 智能Gas价格管理和限制设置
- **并发控制**: 支持并发数量控制避免RPC限制

#### 技术实现

**核心组件**: `src/components/ContractInteraction.vue`

**数据结构**:
```javascript
const walletData = [{
  address: '0x...',      // 钱包地址
  privateKey: '0x...',   // 私钥
  balance: 1.234,        // 余额(ETH)
  status: 'pending'      // 状态: pending/executing/success/failed
}]
```

**归集模式算法**:
```javascript
// 计算最大可转金额
async function calculateMaxTransferAmount(wallet) {
  const balance = await getBalance(wallet.address)
  const gasEstimate = await estimateGas(txObject)
  const gasCost = gasEstimate * gasPrice
  const maxAmount = balance - gasCost
  return Math.max(0, maxAmount)
}
```

**批量执行策略**:
1. **并发限制**: 默认同时执行3个交易避免RPC限制
2. **错误处理**: 单个交易失败不影响其他交易
3. **重试机制**: Gas不足时跳过，其他错误进行重试
4. **状态跟踪**: 实时更新每个钱包的执行状态

**交易构建**:
```javascript
const txObject = {
  from: wallet.address,
  to: targetAddress,
  value: transferValue,
  data: contractData,    // 可选的合约调用数据
  gasPrice: finalGasPrice,
  gas: gasLimit
}

// 签名并发送
const signedTx = await web3.eth.accounts.signTransaction(txObject, privateKey)
const receipt = await web3.eth.sendSignedTransaction(signedTx.rawTransaction)
```

---

## 共享组件和工具系统

### 1. 网络管理系统

**核心组件**: `src/components/network/NetworkSelector.vue`

**预设网络配置**:
```javascript
const NETWORKS = [
  {
    name: '以太坊主网',
    url: 'https://eth-mainnet.g.alchemy.com/v2/...'
  },
  {
    name: '以太坊测试网', 
    url: 'https://eth-sepolia.g.alchemy.com/v2/...'
  },
  {
    name: 'Arbitrum',
    url: 'https://arb-mainnet.g.alchemy.com/v2/...'
  },
  {
    name: 'Base',
    url: 'https://base-mainnet.g.alchemy.com/v2/...'
  },
  {
    name: 'BNB Smart Chain',
    url: 'https://bnb-mainnet.g.alchemy.com/v2/...'
  }
]
```

**网络同步机制**:
- 多个页面间共享网络配置
- localStorage自动同步
- 实时更新Gas价格和网络状态

### 2. Web3管理系统

**核心文件**: `src/composables/useWeb3.js`

**功能特性**:
- Web3实例管理
- RPC连接测试
- Gas价格监控
- 余额查询
- 网络状态检查

**连接测试流程**:
```javascript
async function testRpcConnection(rpcUrl) {
  const testWeb3 = new Web3(rpcUrl)
  
  // 测试1: 获取网络ID
  const chainId = await testWeb3.eth.getChainId()
  
  // 测试2: 获取当前区块号
  const blockNumber = await testWeb3.eth.getBlockNumber()
  
  // 测试3: 获取Gas价格
  const gasPrice = await testWeb3.eth.getGasPrice()
  
  return { chainId, blockNumber, gasPrice }
}
```

### 3. 文件处理系统

**核心文件**: `src/composables/useFileHandler.js`

**支持格式**:
- 钱包文件: `地址 私钥` 或 `私钥` 格式
- 地址文件: `地址,金额` 格式
- 文本文件上传和解析

**解析算法**:
```javascript
// 钱包文件解析
async function parseWalletFile(file, web3Instance) {
  const content = await readTextFile(file)
  const lines = content.trim().split('\n')
  
  for (const line of lines) {
    const parts = line.trim().split(/\s+/)
    if (parts.length === 2) {
      // 地址 + 私钥格式
      const [address, privateKey] = parts
      wallets.push({ address, privateKey })
    } else if (parts.length === 1 && isValidPrivateKey(parts[0])) {
      // 仅私钥格式，从私钥推导地址
      const account = web3Instance.eth.accounts.privateKeyToAccount(privateKey)
      wallets.push({ address: account.address, privateKey })
    }
  }
}
```

### 4. 存储管理系统

**核心文件**: `src/composables/useStorage.js`

**持久化策略**:
- 自动监听数据变化
- 实时保存到localStorage
- 支持默认值设置
- 跨页面数据同步

**使用模式**:
```javascript
const { clearStoredData } = useStorage(
  STORAGE_KEYS.WALLET_GENERATOR,  // 存储键名
  {
    useMnemonic,    // 响应式数据
    numWallets,
    generatedData
  },
  DEFAULT_VALUES.WALLET_GENERATOR  // 默认值
)
```

### 5. 通用UI组件

**基础组件**:
- `BaseCard`: 卡片容器组件
- `BaseButton`: 按钮组件(支持加载状态)
- `FormInput`: 表单输入组件
- `ProgressBar`: 进度条组件
- `StatusGrid`: 状态网格组件
- `FileUpload`: 文件上传组件

**布局组件**:
- `BaseLayout`: 三栏布局组件
- 响应式设计，支持移动端适配

---

## 数据流和状态管理

### 1. 组件通信模式

**父子组件通信**:
```javascript
// 父组件
<NetworkSelector 
  v-model="rpcUrl"
  @network-change="handleNetworkChange"
  @rpc-change="handleRpcChange"
/>

// 子组件
const emit = defineEmits(['update:modelValue', 'network-change'])
emit('network-change', { network, url })
```

**跨页面状态同步**:
```javascript
// 监听localStorage变化
window.addEventListener('storage', (e) => {
  if (e.key === STORAGE_KEYS.CONTRACT_INTERACTION) {
    loadSharedNetworkConfig()
  }
})

// 页面可见性变化时同步
document.addEventListener('visibilitychange', () => {
  if (!document.hidden) {
    loadSharedNetworkConfig()
  }
})
```

### 2. 数据持久化策略

**存储结构**:
```javascript
// localStorage结构
{
  "walletGenerator_data": {
    "useMnemonic": true,
    "numWallets": 50,
    "generatedData": {...}
  },
  "contractInteraction_data": {
    "selectedNetwork": "以太坊测试网",
    "rpcUrl": "https://...",
    "walletData": [...]
  },
  "batchDisperse_data": {
    "contractAddress": "0x...",
    "addressData": [...]
  }
}
```

**同步机制**:
- 网络配置在页面间共享
- 钱包数据独立存储
- 自动保存用户配置
- 支持数据清除和重置

---

## 错误处理和用户体验

### 1. 错误处理策略

**Web3错误处理**:
```javascript
function getErrorMessage(error) {
  if (error.message.includes('reverted')) {
    return 'EVM执行失败: 交易被回滚'
  }
  if (error.message.includes('insufficient funds')) {
    return '余额不足: 请检查钱包余额'
  }
  if (error.message.includes('gas')) {
    return 'Gas相关错误: ' + error.message
  }
  return error.message
}
```

**网络连接处理**:
- RPC连接测试
- 自动重试机制
- 降级处理方案
- 用户友好提示

### 2. 用户体验优化

**进度反馈**:
- 实时进度条显示
- 状态文字更新
- 成功/失败提示

**响应式设计**:
- 移动端适配
- 触摸友好交互
- 灵活布局系统

**性能优化**:
- 大量数据分页加载
- 防抖和节流处理
- 内存泄漏预防

---

## 安全考虑

### 1. 私钥安全

**存储安全**:
- 私钥仅在内存中临时存储
- 不会自动持久化私钥数据
- 用户主动选择是否保存

**传输安全**:
- 所有Web3交互使用HTTPS
- 私钥本地签名，不上传服务器
- 助记词本地生成和处理

### 2. 输入验证

**地址验证**:
```javascript
// 使用Web3内置验证
if (web3.utils.isAddress(address)) {
  // 转换为校验和地址
  address = web3.utils.toChecksumAddress(address)
}
```

**私钥验证**:
```javascript
function isValidPrivateKey(privateKey) {
  const cleanKey = privateKey.startsWith('0x') ? privateKey.slice(2) : privateKey
  return /^[0-9a-fA-F]{64}$/.test(cleanKey) && cleanKey !== '0'.repeat(64)
}
```

### 3. 交易安全

**Gas限制**:
- 用户设定最大Gas价格
- 自动计算合理Gas限制
- 防止Gas耗尽攻击

**余额检查**:
- 交易前验证余额充足
- 考虑Gas费用的实际可用余额
- 防止余额不足导致的交易失败

---

## 部署和构建

### 1. 开发环境配置

**依赖安装**:
```bash
npm install
```

**开发服务器**:
```bash
npm run dev
```

**Node.js版本要求**:
- Node.js: ^20.19.0 || >=22.12.0

### 2. 构建配置

**Vite配置** (`vite.config.js`):
```javascript
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
    nodePolyfills({
      protocolImports: true,
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  define: {
    global: 'globalThis',
  },
  optimizeDeps: {
    include: ['buffer', 'ethers']
  }
})
```

**重要配置说明**:
- `nodePolyfills`: 为浏览器环境提供Node.js polyfills
- `buffer`: 支持加密货币库所需的Buffer对象
- `global`: 解决全局变量兼容性问题

### 3. 生产构建

```bash
npm run build
```

**构建输出**:
- 静态文件输出到`dist/`目录
- 支持任何静态文件服务器托管
- 无需服务端支持，纯前端应用

---

## 扩展和定制

### 1. 添加新网络

**步骤**:
1. 在`src/utils/constants.js`中添加网络配置
2. 更新`NETWORKS`数组
3. 确保RPC端点可用性

**示例**:
```javascript
const NETWORKS = [
  // 现有网络...
  {
    name: '新网络名称',
    url: 'https://新网络rpc端点'
  }
]
```

### 2. 自定义合约交互

**添加新的合约ABI**:
```javascript
const CUSTOM_CONTRACT_ABI = [
  {
    "constant": false,
    "inputs": [...],
    "name": "customFunction",
    "outputs": [...],
    "type": "function"
  }
]
```

**实现新的交互逻辑**:
```javascript
async function callCustomContract() {
  const contract = new web3.eth.Contract(CUSTOM_CONTRACT_ABI, contractAddress)
  const txData = contract.methods.customFunction(...args).encodeABI()
  // 构建和发送交易
}
```

### 3. 添加新功能模块

**创建新组件**:
1. 在`src/components/`中创建新组件
2. 在`src/App.vue`中添加新Tab
3. 实现相应的composable逻辑
4. 添加必要的工具函数

**示例结构**:
```
src/components/NewFeature.vue
src/composables/useNewFeature.js
src/utils/newFeatureHelpers.js
```

---

## 技术细节和最佳实践

### 1. Vue 3 Composition API使用

**响应式数据管理**:
```javascript
import { ref, computed, watch } from 'vue'

// 基础响应式数据
const data = ref(initialValue)

// 计算属性
const computedValue = computed(() => {
  return processData(data.value)
})

// 监听器
watch(data, (newValue, oldValue) => {
  // 处理数据变化
})
```

**组合式函数模式**:
```javascript
export function useCustomLogic() {
  const state = ref()
  
  function action() {
    // 业务逻辑
  }
  
  return {
    state,
    action
  }
}
```

### 2. 异步操作处理

**Promise链式处理**:
```javascript
async function processWallets() {
  try {
    for (const wallet of wallets) {
      await processWallet(wallet)
      // 更新进度
      updateProgress()
    }
  } catch (error) {
    handleError(error)
  }
}
```

**并发控制**:
```javascript
async function batchProcess(items, concurrency = 3) {
  const results = []
  for (let i = 0; i < items.length; i += concurrency) {
    const batch = items.slice(i, i + concurrency)
    const batchResults = await Promise.allSettled(
      batch.map(item => processItem(item))
    )
    results.push(...batchResults)
  }
  return results
}
```

### 3. 性能优化技巧

**大列表渲染优化**:
```javascript
// 使用v-show而不是v-if用于频繁切换
<div v-show="visible">内容</div>

// 使用key属性优化列表渲染
<div v-for="item in items" :key="item.id">{{ item.name }}</div>
```

**内存管理**:
```javascript
// 清理定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('storage', storageHandler)
})
```

---

## 常见问题和解决方案

### 1. Web3连接问题

**问题**: RPC连接失败
**解决方案**:
- 检查RPC端点可用性
- 验证网络配置正确性
- 使用备用RPC端点
- 增加连接超时设置

### 2. Gas价格问题

**问题**: Gas价格过高导致交易失败
**解决方案**:
- 实现智能Gas价格监控
- 提供Gas价格等待机制
- 允许用户自定义Gas设置
- 提供Gas价格预估功能

### 3. 私钥格式问题

**问题**: 私钥格式不正确
**解决方案**:
- 自动添加0x前缀
- 验证私钥长度和格式
- 提供友好的错误提示
- 支持多种私钥输入格式

### 4. 文件解析问题

**问题**: 文件格式不正确
**解决方案**:
- 提供详细的格式说明
- 实现容错解析逻辑
- 显示解析结果预览
- 提供示例文件下载

---

## 总结

AK47Tool是一个功能完整的Web3工具集，采用现代化的前端技术栈构建。该项目具有以下特点：

1. **模块化设计**: 功能模块独立，易于维护和扩展
2. **响应式架构**: 使用Vue 3 Composition API实现响应式数据管理
3. **安全可靠**: 私钥本地处理，不上传服务器
4. **用户友好**: 直观的界面设计和完善的错误处理
5. **扩展性强**: 易于添加新功能和支持新网络

通过本技术文档，开发者可以完全理解系统架构和实现细节，能够使用任何现代化的前端框架重新实现相同的功能。核心的Web3交互逻辑、文件处理算法、和业务流程都有详细的说明和代码示例，为二次开发提供了完整的技术参考。

---

## 附录：关键代码片段

### A. 助记词钱包生成核心算法
```javascript
async function generateWithMnemonic(numWallets, onProgress) {
  const mnemonic = ethers.Wallet.createRandom().mnemonic.phrase
  const addresses = []
  const privateKeys = []
  
  for (let i = 0; i < numWallets; i++) {
    const derivationPath = `m/44'/60'/0'/0/${i}`
    const hdNode = ethers.HDNodeWallet.fromPhrase(mnemonic, undefined, derivationPath)
    
    addresses.push(hdNode.address)
    privateKeys.push({ 
      address: hdNode.address, 
      privateKey: hdNode.privateKey 
    })
    
    if (onProgress) onProgress(i + 1, numWallets)
    if (i % 10 === 0) await new Promise(resolve => setTimeout(resolve, 1))
  }
  
  return { addresses, privateKeys, mnemonic }
}
```

### B. Disperse批量分发核心算法
```javascript
async function executeDisperse() {
  const addresses = addressData.map(item => item.address)
  const amounts = addressData.map(item => 
    web3.utils.toWei(item.amount.toString(), 'ether')
  )
  
  const contract = new web3.eth.Contract(DISPERSE_ABI, contractAddress)
  const txData = contract.methods.disperseEther(addresses, amounts).encodeABI()
  
  const txObject = {
    from: senderAddress,
    to: contractAddress,
    value: amounts.reduce((sum, amount) => BigInt(sum) + BigInt(amount), BigInt(0)).toString(),
    data: txData,
    gasPrice: finalGasPrice,
    gas: gasLimit
  }
  
  const signedTx = await web3.eth.accounts.signTransaction(txObject, privateKey)
  const receipt = await web3.eth.sendSignedTransaction(signedTx.rawTransaction)
  
  return receipt
}
```

### C. 归集模式计算算法
```javascript
async function calculateMaxTransferAmount(wallet, gasPrice, gasLimit) {
  const balance = await web3.eth.getBalance(wallet.address)
  const balanceEth = parseFloat(web3.utils.fromWei(balance, 'ether'))
  
  const gasPriceWei = web3.utils.toWei(gasPrice.toString(), 'gwei')
  const gasCost = parseFloat(web3.utils.fromWei(
    (BigInt(gasLimit) * BigInt(gasPriceWei)).toString(), 
    'ether'
  ))
  
  const maxAmount = balanceEth - gasCost
  return Math.max(0, maxAmount)
}
```

这份技术文档提供了重新实现AK47Tool所需的全部技术细节和架构信息。开发者可以基于此文档使用任何前端技术栈重新构建具有相同功能的Web3工具集。