<template>
  <div v-if="wallets.length > 0" class="wallet-preview">
    <h4>已加载钱包: {{ wallets.length }} 个</h4>
    <div class="preview-list">
      <div 
        v-for="(wallet, index) in wallets" 
        :key="index" 
        class="preview-item"
      >
        <AddressDisplay 
          :address="wallet.address" 
          :max-length="18"
          class="address"
        />
        <span v-if="wallet.balance !== undefined" class="balance">
          {{ formatEthAmount(wallet.balance) }} ETH
        </span>
        <span v-else class="balance loading">获取中...</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import AddressDisplay from '@/components/common/AddressDisplay.vue'
import { formatEthAmount } from '@/utils/formatters'

const props = defineProps({
  wallets: {
    type: Array,
    default: () => []
  }
})
</script>

<style scoped>
.wallet-preview {
  background: rgba(40, 80, 40, 0.6);
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.wallet-preview h4 {
  color: #27ae60;
  margin-bottom: 10px;
}

.preview-list {
  background: rgba(20, 20, 20, 0.6);
  border-radius: 6px;
  padding: 10px;
  max-height: 150px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(107, 155, 209, 0.5) rgba(40, 40, 40, 0.3);
}

.preview-list::-webkit-scrollbar {
  width: 6px;
}

.preview-list::-webkit-scrollbar-track {
  background: rgba(40, 40, 40, 0.3);
  border-radius: 3px;
}

.preview-list::-webkit-scrollbar-thumb {
  background: rgba(107, 155, 209, 0.5);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.preview-list::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 155, 209, 0.8);
}

.preview-item {
  display: grid;
  grid-template-columns: minmax(0, 2fr) minmax(0, 1fr);
  gap: 15px;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(120, 120, 120, 0.3);
  font-size: 13px;
  font-family: 'Courier New', monospace;
}

.preview-item:last-child {
  border-bottom: none;
}

.balance {
  color: #27ae60;
  font-weight: 600;
  font-size: 12px;
  text-align: center;
  white-space: nowrap;
}

.balance.loading {
  color: #d68910;
}



@media (max-width: 768px) {
  .preview-item {
    grid-template-columns: 1fr;
    gap: 8px;
    text-align: left;
  }
  
  .balance {
    grid-row: 2;
    text-align: left;
    justify-self: start;
  }
}
</style> 