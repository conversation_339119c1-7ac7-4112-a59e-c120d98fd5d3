import { ref } from 'vue'
import Web3 from 'web3'

/**
 * Web3相关操作的composable
 */
export function useWeb3() {
  const web3 = ref(null)
  const isTestingRpc = ref(false)
  const rpcConnectionStatus = ref(null) // null: 未测试, true: 连接成功, false: 连接失败
  const currentBlockNumber = ref(null)
  const networkId = ref(null)
  const currentGasPrice = ref(0)
  const rpcTestError = ref('')

  // 初始化Web3
  function initWeb3(rpcUrl) {
    try {
      web3.value = new Web3(rpcUrl)
      console.log('Web3 连接成功')
      return true
    } catch (error) {
      console.error('Web3 连接失败:', error)
      return false
    }
  }

  // 测试RPC连接
  async function testRpcConnection(rpcUrl) {
    if (!rpcUrl) {
      rpcTestError.value = '请先输入RPC端点'
      return false
    }

    isTestingRpc.value = true
    rpcTestError.value = ''
    
    try {
      // 初始化Web3连接
      const testWeb3 = new Web3(rpcUrl)
      
      // 测试1: 获取网络ID
      console.log('正在获取网络ID...')
      const chainId = await testWeb3.eth.getChainId()
      networkId.value = chainId.toString()
      
      // 测试2: 获取当前区块号
      console.log('正在获取当前区块号...')
      const blockNumber = await testWeb3.eth.getBlockNumber()
      currentBlockNumber.value = blockNumber.toString()
      
      // 测试3: 获取Gas价格
      console.log('正在获取Gas价格...')
      const gasPrice = await testWeb3.eth.getGasPrice()
      const gasPriceInGwei = parseFloat(testWeb3.utils.fromWei(gasPrice, 'gwei'))
      currentGasPrice.value = gasPriceInGwei
      
      // 更新Web3实例
      web3.value = testWeb3
      rpcConnectionStatus.value = true
      
      console.log('RPC连接测试成功:', {
        networkId: networkId.value,
        blockNumber: currentBlockNumber.value,
        gasPrice: currentGasPrice.value + ' Gwei'
      })
      
      return true
      
    } catch (error) {
      console.error('RPC连接测试失败:', error)
      rpcConnectionStatus.value = false
      rpcTestError.value = `连接失败: ${error.message}`
      
      // 重置数据
      networkId.value = null
      currentBlockNumber.value = null
      currentGasPrice.value = 0
      
      return false
    } finally {
      isTestingRpc.value = false
    }
  }

  // 获取当前Gas价格
  async function getCurrentGasPrice() {
    try {
      if (!web3.value) return 0
      const gasPrice = await web3.value.eth.getGasPrice()
      return parseFloat(web3.value.utils.fromWei(gasPrice, 'gwei'))
    } catch (error) {
      console.error('获取Gas价格失败:', error)
      return 0
    }
  }

  // 刷新Gas价格
  async function refreshGasPrice() {
    if (!web3.value || !rpcConnectionStatus.value) {
      rpcTestError.value = '请先测试RPC连接'
      return false
    }

    rpcTestError.value = ''
    
    try {
      console.log('正在刷新Gas价格...')
      const gasPrice = await web3.value.eth.getGasPrice()
      const gasPriceInGwei = parseFloat(web3.value.utils.fromWei(gasPrice, 'gwei'))
      currentGasPrice.value = gasPriceInGwei
      
      console.log('Gas价格刷新成功:', currentGasPrice.value + ' Gwei')
      return true
      
    } catch (error) {
      console.error('刷新Gas价格失败:', error)
      rpcTestError.value = `刷新失败: ${error.message}`
      currentGasPrice.value = 0
      return false
    }
  }

  // 获取钱包余额
  async function getBalance(address) {
    try {
      if (!web3.value) return null
      const balance = await web3.value.eth.getBalance(address)
      return parseFloat(web3.value.utils.fromWei(balance, 'ether')).toFixed(4)
    } catch (error) {
      console.warn(`获取地址 ${address} 余额失败:`, error)
      return '获取失败'
    }
  }

  // 批量获取钱包余额
  async function loadWalletBalances(wallets) {
    if (!web3.value) {
      console.warn('Web3未初始化，无法获取余额')
      return wallets
    }

    try {
      const updatedWallets = [...wallets]
      for (let i = 0; i < updatedWallets.length; i++) {
        const balance = await getBalance(updatedWallets[i].address)
        updatedWallets[i].balance = balance
      }
      return updatedWallets
    } catch (error) {
      console.error('获取余额过程中出错:', error)
      return wallets
    }
  }

  // 重置连接状态
  function resetConnectionStatus() {
    rpcConnectionStatus.value = null
    currentBlockNumber.value = null
    networkId.value = null
    currentGasPrice.value = 0
    rpcTestError.value = ''
  }

  // 获取连接状态显示文本
  function getConnectionStatusText() {
    if (rpcConnectionStatus.value === null) {
      return '未测试'
    } else if (rpcConnectionStatus.value === true) {
      return '已连接'
    } else {
      return '连接失败'
    }
  }

  // 获取连接状态样式类
  function getConnectionStatusClass() {
    if (rpcConnectionStatus.value === null) {
      return 'status-unknown'
    } else if (rpcConnectionStatus.value === true) {
      return 'status-success'
    } else {
      return 'status-error'
    }
  }

  return {
    web3,
    isTestingRpc,
    rpcConnectionStatus,
    currentBlockNumber,
    networkId,
    currentGasPrice,
    rpcTestError,
    initWeb3,
    testRpcConnection,
    getCurrentGasPrice,
    refreshGasPrice,
    getBalance,
    loadWalletBalances,
    resetConnectionStatus,
    getConnectionStatusText,
    getConnectionStatusClass
  }
} 