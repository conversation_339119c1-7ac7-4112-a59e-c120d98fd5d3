// 预设网络配置
export const NETWORKS = [
  {
    name: '以太坊主网',
    url: 'https://eth-mainnet.g.alchemy.com/v2/********************************'
  },
  {
    name: '以太坊测试网',
    url: 'https://eth-sepolia.g.alchemy.com/v2/********************************'
  },
  {
    name: 'X Layer',
    url: 'https://xlayer.blockpi.network/v1/rpc/b0b62a7cc9d3882560704ab99f9605bc5ef64aba'
  },
  {
    name: 'Base',
    url: 'https://base-mainnet.g.alchemy.com/v2/********************************'
  },
  {
    name: 'BNB Smart Chain',
    url: 'https://bnb-mainnet.g.alchemy.com/v2/********************************'
  },
  {
    name: 'Arbitrum',
    url: 'https://arb-mainnet.g.alchemy.com/v2/********************************'
  },
]

// 存储键名
export const STORAGE_KEYS = {
  WALLET_GENERATOR: 'walletGenerator_data',
  CONTRACT_INTERACTION: 'contractInteraction_data',
  BATCH_DISPERSE: 'batchDisperse_data'
}

// 默认值配置
export const DEFAULT_VALUES = {
  WALLET_GENERATOR: {
    useMnemonic: true,
    numWallets: 50,
    needAmount: true,
    ethAmount: 0.0001,
    generatedData: {
      addresses: [],
      privateKeys: [],
      mnemonic: ''
    }
  },
  
  CONTRACT_INTERACTION: {
    selectedNetwork: '以太坊测试网',
    rpcUrl: 'https://eth-sepolia.g.alchemy.com/v2/********************************',
    targetAddress: '',
    contractData: '',
    transferValue: 0,
    useCollectionMode: false,
    gasLimit: 21000,
    maxGasPrice: 0, // 将在运行时设置为实时Gas价格
    additionalGas: 0.01,
    walletData: [],
    transactionResults: [],
    rpcConnectionStatus: null,
    currentBlockNumber: null,
    networkId: null,
    currentGasPrice: 0
  },

  BATCH_DISPERSE: {
    useContractDisperse: true, // 默认使用合约分发
    contractAddress: '******************************************',
    privateKey: '',
    addressData: [],
    transactionResults: [],
    maxGasPrice: 0, // 将在运行时设置为实时Gas价格
    additionalGas: 0
  }
}

// 文件类型配置
export const FILE_TYPES = {
  TXT: '.txt',
  ACCEPTED_UPLOAD_TYPES: '.txt'
}

// Gas 相关常量
export const GAS_CONFIG = {
  STANDARD_TRANSFER_LIMIT: 21000,
  MIN_GAS_LIMIT: 21000,
  DEFAULT_MAX_GAS_PRICE: 1,
  DEFAULT_ADDITIONAL_GAS: 0.1
}

// UI 配置
export const UI_CONFIG = {
  MAX_PREVIEW_WALLETS: 3,
  MAX_RECENT_TRANSACTIONS: 10,
  PROGRESS_UPDATE_INTERVAL: 10,
  GAS_REFRESH_DELAY: 3000
} 