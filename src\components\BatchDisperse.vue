<template>
  <div class="batch-disperse">
    <div class="container">
      <div class="main-content">
        <!-- 左侧配置面板 -->
        <div class="left-panel">
          <BaseCard title="网络配置" variant="config">
            <!-- 网络选择器 -->
            <NetworkSelector
                v-model="rpcUrl"
                :default-network="selectedNetwork"
                @network-change="handleNetworkChange"
                @rpc-change="handleRpcChange"
            />

            <!-- RPC测试 -->
            <RpcTester
                :rpc-url="rpcUrl"
                @connection-status="handleConnectionStatus"
                @gas-price-update="handleGasPriceUpdate"
            />
          </BaseCard>

          <BaseCard title="批量分发配置" variant="config">
            <!-- 合约分发开关 -->
            <div class="form-group">
              <label class="form-label">分发模式</label>
              <div class="switch-container">
                <label class="switch">
                  <input 
                    type="checkbox" 
                    v-model="useContractDisperse"
                    @change="handleContractModeChange"
                  />
                  <span class="slider"></span>
                </label>
                <span class="switch-label">
                  {{ useContractDisperse ? '合约分发（默认）' : '私钥逐笔分发' }}
                </span>
              </div>
              <div class="mode-description">
                <p v-if="useContractDisperse" class="mode-desc">
                  🔧 使用Disperse合约一次性向多个地址分发ETH，Gas费用较低
                </p>
                <p v-else class="mode-desc">
                  🔑 使用私钥向每个地址单独发送交易，不依赖合约，nonce自动递增
                </p>
              </div>
            </div>
            
            <FormInput
              v-model="privateKey"
              label="发送者私钥"
              type="password"
              placeholder="输入私钥"
              wide
            />

            <!-- 刷新nonce按钮 -->
            <div v-if="!useContractDisperse && privateKey.trim()" class="nonce-section">
              <div class="nonce-info">
                <label class="form-label">当前Nonce</label>
                <div class="nonce-display">
                  <span class="nonce-value">{{ currentNonce !== null ? currentNonce : '未获取' }}</span>
                  <BaseButton
                    variant="secondary"
                    size="small"
                    :disabled="isRefreshingNonce"
                    :loading="isRefreshingNonce"
                    :loading-text="'刷新中...'"
                    @click="refreshNonce"
                  >
                    🔄 刷新
                  </BaseButton>
                </div>
              </div>
            </div>
            <FormInput
              v-if="useContractDisperse"
              v-model="contractAddress"
              label="Disperse合约地址"
              type="text"
              placeholder="0x..."
              wide
            />
          </BaseCard>

          <BaseCard title="Gas价格设置" variant="config">
            <FormInput
              v-model="maxGasPrice"
              label="最大Gas价格 (Gwei)"
              type="number"
              :step="0.001"
              :min="0"
            />
            <FormInput
              v-model="additionalGas"
              label="额外Gas (Gwei)"
              type="number"
              :step="0.001"
              :min="0"
            />
          </BaseCard>
        </div>

        <!-- 中间地址面板 -->
        <div class="middle-panel">
          <BaseCard title="地址数据" variant="config">
            <!-- 文件格式帮助 -->
            <FormatHelp>
              <FormatItem
                title="格式: 地址,金额 (逗号分隔)"
                :examples="['0x1234...abcd,0.001', '0x5678...efgh,0.002']"
                description="每行一个地址和对应的ETH金额"
              />
            </FormatHelp>

            <!-- 数据输入 -->
            <DataInput
                accepted-types=".txt"
                file-description="支持 .txt 文件格式 (地址,金额)"
                text-input-label="直接输入地址数据"
                text-placeholder="请输入地址数据，每行一条记录：&#10;格式: 地址,金额 (逗号分隔)&#10;&#10;示例:&#10;0x1234...abcd,0.001&#10;0x5678...efgh,0.002&#10;0x9abc...1234,0.005"
                text-input-description="💡 格式：地址,金额 (逗号分隔)，每行一个地址和对应的ETH金额"
                @data-parsed="handleDataParsed"
                @error="handleFileError"
            />

            <!-- 地址预览 -->
            <AddressPreview :addresses="addressData" />
          </BaseCard>

          <ActionButtonGroup>
            <template #buttons>
              <BaseButton
                  variant="success"
                  :disabled="isRunning || addressData.length === 0 || !privateKey.trim()"
                  :loading="isRunning"
                  :loading-text="'执行中...'"
                  @click="startDisperse"
              >
                🚀 开始批量分发
              </BaseButton>
              <BaseButton
                  variant="danger"
                  :disabled="!isRunning"
                  @click="stopDisperse"
              >
                ⏹️ 停止执行
              </BaseButton>
              <BaseButton
                  variant="danger"
                  @click="handleClearData"
              >
                🗑️ 清除保存的数据
              </BaseButton>
            </template>
          </ActionButtonGroup>
        </div>

        <!-- 右侧状态面板 -->
        <div class="right-panel">
          <!-- 执行状态 -->
          <BaseCard v-if="isRunning" title="📊 执行状态" variant="status">
            <StatusGrid :items="executionStatusItems" class="execution-status-grid" />

            <ProgressBar
                :current="executedCount"
                :total="useContractDisperse ? 1 : addressData.length"
                :message="statusMessage"
            />
          </BaseCard>

          <!-- 发送者信息 -->
          <WalletInfo
            :address="senderInfo.address"
            :balance="senderInfo.balance"
            :required-amount="Number(totalAmount)"
            :is-refreshing="isRefreshingBalance"
            @refresh="refreshBalance"
          />

          <!-- 交易记录 -->
          <TransactionResults
            :results="transactionResults"
            :max-display="recentTransactionResults.length"
            @clear="clearTransactionResults"
          >
            <template #result-info="{ result }">
              <span class="result-status">{{ result.success ? '✅ 成功' : '❌ 失败' }}</span>
              <span class="tx-amount">
                {{ result.totalAmount || result.amount }} ETH → 
                {{ result.recipientCount || 1 }} 个地址
                <span v-if="result.recipient" class="recipient-info">
                  ({{ result.recipient.slice(0, 8) + '...' + result.recipient.slice(-6) }})
                </span>
              </span>
            </template>
          </TransactionResults>
        </div>
      </div>
    </div>

    <!-- 确认弹窗 -->
    <div v-if="showConfirmDialog" class="modal-overlay" @click="closeConfirmDialog">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>📋 批量分发确认</h3>
          <button @click="closeConfirmDialog" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <div class="confirm-item">
            <span class="label">分发地址数量:</span>
            <span class="value">{{ addressData.length }} 个地址</span>
          </div>
          <div class="confirm-item">
            <span class="label">分发总额:</span>
            <span class="value">{{ totalAmount.toFixed(6) }} ETH</span>
          </div>
          <div class="confirm-item">
            <span class="label">Gas 预估:</span>
            <span class="value">{{ gasEstimate.toLocaleString() }}</span>
          </div>
          <div class="confirm-item">
            <span class="label">Gas Price:</span>
            <span class="value" :class="gasPriceGwei >= maxGasPrice ? 'gas-high' : 'gas-normal'">
              {{ gasPriceGwei.toFixed(4) }} Gwei
              {{ gasPriceGwei >= maxGasPrice ? '(过高，将等待下降)' : '(合适)' }}
            </span>
          </div>
          <div class="confirm-item">
            <span class="label">预估手续费:</span>
            <span class="value">{{ estimatedFee.toFixed(12) }} ETH</span>
          </div>
          <div class="confirm-item total">
            <span class="label">总计花费:</span>
            <span class="value">{{ (Number(totalAmount) + Number(estimatedFee)).toFixed(6) }} ETH</span>
          </div>
        </div>
        <div class="modal-footer">
          <BaseButton
            variant="secondary"
            @click="closeConfirmDialog"
          >
            取消
          </BaseButton>
          <BaseButton
            variant="success"
            :disabled="isEstimating"
            :loading="isEstimating"
            :loading-text="'预估中...'"
            @click="confirmAndExecute"
          >
            确认执行
          </BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// 组件导入
import NetworkSelector from '@/components/network/NetworkSelector.vue'
import RpcTester from '@/components/network/RpcTester.vue'
import BaseCard from '@/components/common/BaseCard.vue'
import BaseButton from '@/components/common/BaseButton.vue'
import FormInput from '@/components/common/FormInput.vue'
import DataInput from '@/components/common/DataInput.vue'
import StatusGrid from '@/components/common/StatusGrid.vue'
import ProgressBar from '@/components/common/ProgressBar.vue'
import FormatHelp from '@/components/common/FormatHelp.vue'
import FormatItem from '@/components/common/FormatItem.vue'
import ActionButtonGroup from '@/components/common/ActionButtonGroup.vue'
import WalletInfo from '@/components/common/WalletInfo.vue'
import AddressPreview from '@/components/common/AddressPreview.vue'
import TransactionResults from '@/components/common/TransactionResults.vue'

// Composables导入
import { useStorage } from '@/composables/useStorage'
import { useWeb3 } from '@/composables/useWeb3'
import { useFileHandler } from '@/composables/useFileHandler'

// 工具函数导入
import { STORAGE_KEYS, DEFAULT_VALUES, UI_CONFIG } from '@/utils/constants'
import { formatTxHash, formatGasPrice } from '@/utils/formatters'
import { sleep, getErrorMessage } from '@/utils/helpers'

// Disperse合约ABI
const DISPERSE_ABI = [
  {
    "constant": false,
    "inputs": [
      {"name": "recipients", "type": "address[]"},
      {"name": "values", "type": "uint256[]"}
    ],
    "name": "disperseEther",
    "outputs": [],
    "payable": true,
    "stateMutability": "payable",
    "type": "function"
  }
]

// 从合约交互共享的网络配置状态
const contractInteractionData = JSON.parse(localStorage.getItem(STORAGE_KEYS.CONTRACT_INTERACTION) || '{}')

// 共享的网络配置
const selectedNetwork = ref(contractInteractionData.selectedNetwork || '以太坊测试网')
const rpcUrl = ref(contractInteractionData.rpcUrl || 'https://eth-sepolia.g.alchemy.com/v2/********************************')

// 批量分发专有配置
const useContractDisperse = ref(true) // 默认使用合约分发
const contractAddress = ref('******************************************')
const privateKey = ref('')
const maxGasPrice = ref(0) // 将在初始化时设置为实时Gas价格
const additionalGas = ref(0)

const addressData = ref([])
const isRunning = ref(false)
const executedCount = ref(0)
const statusMessage = ref('')
const transactionResults = ref([])
const senderInfo = ref({ address: '', balance: 0 })
const isRefreshingBalance = ref(false)

// nonce相关状态
const currentNonce = ref(null)
const isRefreshingNonce = ref(false)

// 确认弹窗相关
const showConfirmDialog = ref(false)
const isEstimating = ref(false)
const gasEstimate = ref(0)
const gasPriceGwei = ref(0)
const estimatedFee = ref(0)

let stopFlag = false

// 使用composables
const {
  web3,
  currentGasPrice,
  initWeb3,
  getCurrentGasPrice,
  getBalance
} = useWeb3()

const { parseAddressFile } = useFileHandler()

// 批量分发自己的存储（不包含网络配置）
const { clearStoredData } = useStorage(
    STORAGE_KEYS.BATCH_DISPERSE,
    {
      useContractDisperse,
      contractAddress,
      privateKey,
      addressData,
      transactionResults,
      maxGasPrice,
      additionalGas
    },
    DEFAULT_VALUES.BATCH_DISPERSE
)

// 网络配置变化时同步到合约交互的存储
function syncNetworkConfigToContract() {
  const contractData = JSON.parse(localStorage.getItem(STORAGE_KEYS.CONTRACT_INTERACTION) || '{}')
  contractData.selectedNetwork = selectedNetwork.value
  contractData.rpcUrl = rpcUrl.value
  localStorage.setItem(STORAGE_KEYS.CONTRACT_INTERACTION, JSON.stringify(contractData))
}

// 处理合约分发模式变化
function handleContractModeChange() {
  console.log('分发模式切换为:', useContractDisperse.value ? '合约分发' : '私钥逐笔分发')
  // 清除之前的交易记录，因为不同模式的记录格式可能不同
  transactionResults.value = []
  // 切换到私钥模式时刷新nonce
  if (!useContractDisperse.value && privateKey.value.trim()) {
    setTimeout(() => refreshNonce(), 100)
  }
}

// 刷新nonce
async function refreshNonce() {
  if (!privateKey.value.trim() || isRefreshingNonce.value) {
    return
  }

  // 确保Web3已初始化
  if (!web3.value) {
    initWeb3(rpcUrl.value)
  }
  
  if (!web3.value) {
    console.error('Web3初始化失败')
    return
  }

  isRefreshingNonce.value = true

  try {
    console.log('刷新nonce，使用RPC端点:', rpcUrl.value)
    const formattedPrivateKey = privateKey.value.startsWith('0x') ? privateKey.value : '0x' + privateKey.value
    const account = web3.value.eth.accounts.privateKeyToAccount(formattedPrivateKey)
    const nonce = await web3.value.eth.getTransactionCount(account.address)

    currentNonce.value = Number(nonce) // 转换为普通数字
    console.log('nonce刷新完成:', Number(nonce))
  } catch (error) {
    console.error('刷新nonce失败:', error)
    alert('刷新nonce失败: ' + getErrorMessage(error))
    currentNonce.value = null
  } finally {
    isRefreshingNonce.value = false
  }
}

// 计算属性

const totalAmount = computed(() => {
  return addressData.value.reduce((sum, item) => sum + item.amount, 0)
})

const executionStatusItems = computed(() => [
  {
    label: '当前Gas价格',
    value: formatGasPrice(currentGasPrice.value),
    status: currentGasPrice.value > maxGasPrice.value ? 'gas-high' : 'success'
  },
  {
    label: '地址数量',
    value: addressData.value.length.toString(),
    status: 'success'
  },
  {
    label: '总分发金额',
    value: `${totalAmount.value.toFixed(6)} ETH`,
    status: 'success'
  },
  {
    label: '发送者余额',
    value: `${senderInfo.value.balance.toFixed(6)} ETH`,
    status: senderInfo.value.balance >= Number(totalAmount.value) ? 'success' : 'error'
  }
])

const recentTransactionResults = computed(() => {
  return transactionResults.value.slice(-UI_CONFIG.MAX_RECENT_TRANSACTIONS).length
})

// 网络变化处理
async function handleNetworkChange(data) {
  selectedNetwork.value = data.network
  rpcUrl.value = data.url
  syncNetworkConfigToContract() // 同步到合约交互
  // 切换RPC/网络时重置额外Gas为0，避免沿用旧配置
  additionalGas.value = 0
  
  // 网络切换后总是重新初始化Web3并刷新数据
  console.log('网络切换到:', data.network, data.url)
  initWeb3(data.url)
  
  // 延迟一下等待连接稳定后刷新所有数据
  setTimeout(async () => {
    const gasPrice = await getCurrentGasPrice()
    if (gasPrice > 0) {
      currentGasPrice.value = gasPrice
      const rounded = Number(gasPrice.toFixed(4))
      // 网络切换时也更新最大Gas，保证下拉切换网络生效
      maxGasPrice.value = rounded
      console.log('网络切换后更新最大Gas为实时价格:', rounded, 'Gwei')
    }
    
    // 如果有私钥，同时刷新发送者余额信息
    if (privateKey.value.trim()) {
      console.log('网络切换后刷新发送者余额...')
      await updateSenderInfo()
      // 如果是私钥分发模式，同时刷新nonce
      if (!useContractDisperse.value) {
        await refreshNonce()
      }
    }
  }, 500)
}

async function handleRpcChange(url) {
  rpcUrl.value = url
  syncNetworkConfigToContract() // 同步到合约交互
  // 切换RPC时重置额外Gas为0
  additionalGas.value = 0
  
  // RPC切换后总是重新初始化Web3并刷新所有数据
  console.log('RPC切换到:', url)
  initWeb3(url)
  
  // 延迟一下等待连接稳定后刷新所有数据
  setTimeout(async () => {
    const gasPrice = await getCurrentGasPrice()
    if (gasPrice > 0) {
      currentGasPrice.value = gasPrice
      const rounded = Number(gasPrice.toFixed(4))
      // RPC切换时总是更新最大Gas
      maxGasPrice.value = rounded
      console.log('RPC切换后更新最大Gas为实时价格:', rounded, 'Gwei')
    }
    
    // 如果有私钥，同时刷新发送者余额信息
    if (privateKey.value.trim()) {
      console.log('RPC切换后刷新发送者余额...')
      await updateSenderInfo()
      // 如果是私钥分发模式，同时刷新nonce
      if (!useContractDisperse.value) {
        await refreshNonce()
      }
    }
  }, 500)
}

function handleConnectionStatus(success) {
  if (success) {
    console.log('RPC连接成功')
  }
}

function handleGasPriceUpdate(gasPrice) {
  currentGasPrice.value = gasPrice
}

// 数据处理
async function handleDataParsed(file, inputType) {
  try {
    console.log(`处理地址数据 (${inputType === 'text' ? '文本输入' : '文件上传'})，使用RPC端点:`, rpcUrl.value)
    // 确保Web3已初始化
    if (!web3.value) {
      initWeb3(rpcUrl.value)
    }

    const addresses = await parseAddressFile(file, web3.value)

    if (addresses.length === 0) {
      alert('未能解析到有效的地址数据，请检查文件格式')
      return
    }

    addressData.value = addresses
    alert(`成功加载 ${addresses.length} 个地址，总金额: ${totalAmount.value.toFixed(6)} ETH`)

    // 如果有私钥，更新发送者信息
    if (privateKey.value.trim()) {
      await updateSenderInfo()
    }
  } catch (error) {
    console.error('文件处理失败:', error)
    alert('文件处理失败: ' + getErrorMessage(error))
  }
}

function handleFileError(error) {
  console.error('文件上传错误:', error)
}

// 更新发送者信息
async function updateSenderInfo() {
  try {
    if (!privateKey.value.trim()) {
      senderInfo.value = { address: '', balance: 0 }
      return
    }

    // 确保使用最新的Web3实例
    if (!web3.value) {
      initWeb3(rpcUrl.value)
    }
    
    if (!web3.value) {
      console.error('Web3初始化失败')
      senderInfo.value = { address: '', balance: 0 }
      return
    }

    console.log('使用RPC端点获取发送者信息:', rpcUrl.value)
    const formattedPrivateKey = privateKey.value.startsWith('0x') ? privateKey.value : '0x' + privateKey.value
    const account = web3.value.eth.accounts.privateKeyToAccount(formattedPrivateKey)
    const balance = await getBalance(account.address)

    senderInfo.value = {
      address: account.address,
      balance: parseFloat(balance) || 0
    }
    
    console.log('发送者信息更新完成:', account.address, balance, 'ETH')
  } catch (error) {
    console.error('获取发送者信息失败:', error)
    senderInfo.value = { address: '', balance: 0 }
  }
}

// 刷新余额
async function refreshBalance() {
  if (!privateKey.value.trim() || isRefreshingBalance.value) {
    return
  }

  // 确保使用最新的Web3实例
  if (!web3.value) {
    initWeb3(rpcUrl.value)
  }
  
  if (!web3.value) {
    console.error('Web3初始化失败')
    return
  }

  isRefreshingBalance.value = true

  try {
    console.log('使用RPC端点刷新余额:', rpcUrl.value)
    const formattedPrivateKey = privateKey.value.startsWith('0x') ? privateKey.value : '0x' + privateKey.value
    const account = web3.value.eth.accounts.privateKeyToAccount(formattedPrivateKey)
    const balance = await getBalance(account.address)

    senderInfo.value = {
      address: account.address,
      balance: parseFloat(balance) || 0
    }

    console.log('余额刷新完成:', balance, 'ETH')
  } catch (error) {
    console.error('刷新余额失败:', error)
    alert('刷新余额失败: ' + getErrorMessage(error))
  } finally {
    isRefreshingBalance.value = false
  }
}

// 清除数据
async function handleClearData() {
  try {
    await clearStoredData()
    addressData.value = []
    transactionResults.value = []
    senderInfo.value = { address: '', balance: 0 }
  } catch (error) {
    // 用户取消操作
  }
}

// 清除交易记录
function clearTransactionResults() {
  if (confirm('确定要清除所有交易记录吗？')) {
    transactionResults.value = []
  }
}

// 预估Gas和费用
async function estimateGasAndFee() {
  try {
    isEstimating.value = true

    // 确保使用最新的Web3实例
    if (!web3.value) {
      console.log('预估Gas时重新初始化Web3，使用RPC:', rpcUrl.value)
      initWeb3(rpcUrl.value)
    }
    
    if (!web3.value) {
      throw new Error('Web3初始化失败')
    }

    console.log('预估Gas和费用，使用RPC端点:', rpcUrl.value)

    // 获取当前gas价格
    const currentGasPriceBN = await web3.value.eth.getGasPrice()
    const additionalGasWei = web3.value.utils.toWei(additionalGas.value.toString(), 'gwei')
    const finalGasPrice = (BigInt(currentGasPriceBN) + BigInt(additionalGasWei)).toString()
    
    gasPriceGwei.value = parseFloat(web3.value.utils.fromWei(finalGasPrice, 'gwei'))

    if (useContractDisperse.value) {
      // 合约分发模式的预估
      const addresses = addressData.value.map(item => item.address)
      const amounts = addressData.value.map(item => web3.value.utils.toWei(item.amount.toString(), 'ether'))

      // 计算总金额
      const totalAmountWei = amounts.reduce((sum, amount) => {
        return BigInt(sum) + BigInt(amount)
      }, BigInt(0))

      // 创建合约实例
      const contract = new web3.value.eth.Contract(DISPERSE_ABI, contractAddress.value)

      // 获取发送者账户
      const formattedPrivateKey = privateKey.value.startsWith('0x') ? privateKey.value : '0x' + privateKey.value
      const account = web3.value.eth.accounts.privateKeyToAccount(formattedPrivateKey)
      const fromAddress = account.address

      // 构建交易数据用于预估
      const txData = contract.methods.disperseEther(addresses, amounts).encodeABI()
      const estimateObject = {
        from: fromAddress,
        to: contractAddress.value,
        value: totalAmountWei.toString(),
        data: txData,
        gasPrice: finalGasPrice
      }

      // 预估gas
      const estimatedGas = await web3.value.eth.estimateGas(estimateObject)

      // 设置预估值
      gasEstimate.value = Number(estimatedGas)
      estimatedFee.value = parseFloat(web3.value.utils.fromWei((BigInt(estimatedGas) * BigInt(finalGasPrice)).toString(), 'ether'))

      console.log(`合约分发预估: 向 ${addresses.length} 个地址分发，总额 ${web3.value.utils.fromWei(totalAmountWei, 'ether')} ETH`)
      console.log(`Gas 预估: ${estimatedGas}`)
      console.log(`Gas Price: ${gasPriceGwei.value} Gwei`)
      console.log(`预估手续费: ${estimatedFee.value} ETH`)
    } else {
      // 私钥分发模式的预估
      const standardGasLimit = 21000 // ETH转账标准gas limit
      const totalGas = standardGasLimit * addressData.value.length
      
      gasEstimate.value = totalGas
      estimatedFee.value = parseFloat(web3.value.utils.fromWei((BigInt(totalGas.toString()) * BigInt(finalGasPrice)).toString(), 'ether'))

      console.log(`私钥分发预估: 向 ${addressData.value.length} 个地址分发，每笔 ${standardGasLimit} gas`)
      console.log(`总Gas预估: ${totalGas}`)
      console.log(`Gas Price: ${gasPriceGwei.value} Gwei`)
      console.log(`预估手续费: ${estimatedFee.value} ETH`)
    }

    return true
  } catch (error) {
    console.error('Gas预估失败:', error)
    alert('Gas预估失败: ' + getErrorMessage(error))
    return false
  } finally {
    isEstimating.value = false
  }
}

// 执行私钥逐笔分发
async function executePrivateKeyDisperse() {
  try {
    // 确保使用最新的Web3实例
    if (!web3.value) {
      console.log('执行私钥分发时重新初始化Web3，使用RPC:', rpcUrl.value)
      initWeb3(rpcUrl.value)
    }
    
    if (!web3.value) {
      throw new Error('Web3初始化失败')
    }

    console.log('执行私钥逐笔分发，使用RPC端点:', rpcUrl.value)
    
    // 获取发送者账户
    const formattedPrivateKey = privateKey.value.startsWith('0x') ? privateKey.value : '0x' + privateKey.value
    const account = web3.value.eth.accounts.privateKeyToAccount(formattedPrivateKey)
    const fromAddress = account.address

    // 使用当前显示的nonce，如果没有则重新获取
    let startingNonce = currentNonce.value
    if (startingNonce === null) {
      const nonce = await web3.value.eth.getTransactionCount(fromAddress)
      startingNonce = Number(nonce) // 转换为普通数字
      currentNonce.value = startingNonce
    }
    console.log('使用起始nonce:', startingNonce)

    // 获取当前gas价格
    const currentGasPriceBN = await web3.value.eth.getGasPrice()
    const additionalGasWei = web3.value.utils.toWei(additionalGas.value.toString(), 'gwei')
    const finalGasPrice = (BigInt(currentGasPriceBN) + BigInt(additionalGasWei)).toString()

    // 预估单笔交易的gas limit
    const gasLimit = '21000' // ETH转账的标准gas limit

    const results = []
    
    // 逐笔发送交易
    for (let i = 0; i < addressData.value.length && !stopFlag; i++) {
      const addressItem = addressData.value[i]
      const recipient = addressItem.address
      const amount = addressItem.amount
      const amountWei = web3.value.utils.toWei(amount.toString(), 'ether')

      // 使用当前nonce值（在try外定义，确保catch块也能访问）
      const txNonce = startingNonce + i

      try {
        statusMessage.value = `正在发送第 ${i + 1}/${addressData.value.length} 笔交易到 ${recipient.slice(0, 8)}...`

        // 构建交易对象
        const txObject = {
          from: fromAddress,
          to: recipient,
          value: amountWei,
          nonce: txNonce,
          gasPrice: finalGasPrice,
          gas: gasLimit
        }

        console.log(`发送第 ${i + 1} 笔交易:`, {
          to: recipient,
          amount: amount + ' ETH',
          nonce: txNonce
        })

        // 签名交易
        const signedTx = await web3.value.eth.accounts.signTransaction(txObject, formattedPrivateKey)

        // 发送交易（不等待确认）
        const txHash = await new Promise((resolve, reject) => {
          web3.value.eth.sendSignedTransaction(signedTx.rawTransaction)
            .on('transactionHash', function(hash) {
              console.log(`第 ${i + 1} 笔交易已发送, hash:`, hash)
              resolve(hash)
            })
            .on('error', function(error) {
              console.error(`第 ${i + 1} 笔交易发送失败:`, error.message)
              reject(error)
            })
            .catch(function(error) {
              console.error(`第 ${i + 1} 笔交易发送失败:`, error.message)
              reject(error)
            })

          // 超时保护
          setTimeout(() => {
            reject(new Error('交易发送超时'))
          }, 10000)
        })

        // 记录成功结果
        const result = {
          success: true,
          txHash: txHash,
          amount: amount.toFixed(6),
          recipient: recipient,
          nonce: txNonce
        }
        results.push(result)
        transactionResults.value.push(result)

        // 更新执行计数
        executedCount.value = i + 1

        // 更新当前nonce显示
        currentNonce.value = txNonce + 1

        // 短暂延迟，避免发送过快
        await sleep(0.1)

      } catch (error) {
        console.error(`第 ${i + 1} 笔交易失败:`, error.message)
        
        // 记录失败结果
        const errorResult = {
          success: false,
          error: `交易失败: ${getErrorMessage(error)}`,
          amount: amount.toFixed(6),
          recipient: recipient,
          nonce: txNonce
        }
        results.push(errorResult)
        transactionResults.value.push(errorResult)

        // 更新当前nonce显示（即使失败也要更新）
        currentNonce.value = txNonce + 1
      }
    }

    return {
      success: results.length > 0,
      totalSent: results.filter(r => r.success).length,
      totalFailed: results.filter(r => !r.success).length,
      results: results
    }

  } catch (error) {
    console.error('私钥逐笔分发失败:', error)
    const errorResult = {
      success: false,
      error: getErrorMessage(error),
      totalAmount: totalAmount.value.toFixed(6),
      recipientCount: addressData.value.length
    }
    transactionResults.value.push(errorResult)
    return errorResult
  }
}

// 执行批量分发
async function executeDisperse() {
  try {
    // 确保使用最新的Web3实例
    if (!web3.value) {
      console.log('执行分发时重新初始化Web3，使用RPC:', rpcUrl.value)
      initWeb3(rpcUrl.value)
    }
    
    if (!web3.value) {
      throw new Error('Web3初始化失败')
    }

    console.log('执行批量分发，使用RPC端点:', rpcUrl.value)
    
    // 准备数据
    const addresses = addressData.value.map(item => item.address)
    const amounts = addressData.value.map(item => web3.value.utils.toWei(item.amount.toString(), 'ether'))

    // 计算总金额
    const totalAmountWei = amounts.reduce((sum, amount) => {
      return BigInt(sum) + BigInt(amount)
    }, BigInt(0))

    // 创建合约实例
    const contract = new web3.value.eth.Contract(DISPERSE_ABI, contractAddress.value)

    // 获取发送者账户
    const formattedPrivateKey = privateKey.value.startsWith('0x') ? privateKey.value : '0x' + privateKey.value
    const account = web3.value.eth.accounts.privateKeyToAccount(formattedPrivateKey)
    const fromAddress = account.address

    // 获取nonce和gas价格
    const nonce = Number(await web3.value.eth.getTransactionCount(fromAddress))
    const currentGasPriceBN = await web3.value.eth.getGasPrice()
    const additionalGasWei = web3.value.utils.toWei(additionalGas.value.toString(), 'gwei')
    const finalGasPrice = (BigInt(currentGasPriceBN) + BigInt(additionalGasWei)).toString()

    // 构建交易数据
    const txData = contract.methods.disperseEther(addresses, amounts).encodeABI()

    // 使用预估的gas * 1.1
    const finalGasLimit = Math.floor(Number(gasEstimate.value) * 1.1)

    const txObject = {
      from: fromAddress,
      to: contractAddress.value,
      value: totalAmountWei.toString(),
      data: txData,
      nonce: nonce,
      gasPrice: finalGasPrice,
      gas: finalGasLimit
    }

    console.log(`使用Gas限制: ${finalGasLimit} (预估的${Number(gasEstimate.value)} * 1.1)`)

    // 签名并发送交易（在收到 txHash 时立即返回）
    const signedTx = await web3.value.eth.accounts.signTransaction(txObject, formattedPrivateKey)

    statusMessage.value = '正在发送交易...'

    // 预先计算txHash以便在错误时也能展示
    const precomputedTxHash = web3.value.utils.keccak256(signedTx.rawTransaction)

    try {
      return await new Promise((resolve) => {
        web3.value.eth.sendSignedTransaction(signedTx.rawTransaction)
          .on('transactionHash', function(hash) {
            console.log('交易已发送, hash:', hash)
            const result = {
              success: true,
              txHash: hash,
              totalAmount: totalAmount.value.toFixed(6),
              recipientCount: addresses.length
            }
            transactionResults.value.push(result)
            resolve(result)
          })
          .on('error', function(error) {
            console.error('交易执行失败:', error.message)
            const errorResult = {
              success: false,
              error: `交易失败: ${getErrorMessage(error)}`,
              txHash: precomputedTxHash,
              totalAmount: totalAmount.value.toFixed(6),
              recipientCount: addresses.length
            }
            transactionResults.value.push(errorResult)
            resolve(errorResult)
          })
          .catch(function(error) {
            console.error('交易发送失败:', error.message)
            const errorResult = {
              success: false,
              error: `发送失败: ${getErrorMessage(error)}`,
              txHash: precomputedTxHash,
              totalAmount: totalAmount.value.toFixed(6),
              recipientCount: addresses.length
            }
            transactionResults.value.push(errorResult)
            resolve(errorResult)
          })

        // 超时保护，防止异常情况下长时间无响应
        setTimeout(() => {
          const timeoutResult = {
            success: false,
            error: '交易发送超时',
            txHash: precomputedTxHash,
            totalAmount: totalAmount.value.toFixed(6),
            recipientCount: addresses.length
          }
          transactionResults.value.push(timeoutResult)
          resolve(timeoutResult)
        }, 30000)
      })
    } catch (sendError) {
      console.error('交易发送失败:', sendError.message)
      const errorResult = {
        success: false,
        error: `交易发送失败: ${getErrorMessage(sendError)}`,
        txHash: precomputedTxHash,
        totalAmount: totalAmount.value.toFixed(6),
        recipientCount: addresses.length
      }
      transactionResults.value.push(errorResult)
      return errorResult
    }

  } catch (error) {
    console.error('执行批量分发失败:', error)
    const errorResult = {
      success: false,
      error: getErrorMessage(error),
      totalAmount: totalAmount.value.toFixed(6),
      recipientCount: addressData.value.length
    }
    transactionResults.value.push(errorResult)
    return errorResult
  }
}

// 开始分发（显示确认弹窗）
async function startDisperse() {
  if (addressData.value.length === 0) {
    alert('请先上传地址文件')
    return
  }

  if (!privateKey.value.trim()) {
    alert('请输入发送者私钥')
    return
  }

  // 确保使用最新的Web3实例
  console.log('开始分发，使用RPC端点:', rpcUrl.value)
  initWeb3(rpcUrl.value)

  if (!web3.value) {
    alert('Web3初始化失败，请检查RPC端点')
    return
  }

  // 更新发送者信息（使用最新的Web3实例）
  await updateSenderInfo()

  // 检查余额（基本检查，详细检查在预估后）
  if (senderInfo.value.balance < Number(totalAmount.value)) {
    alert(`余额不足！需要至少 ${totalAmount.value.toFixed(6)} ETH，当前余额 ${senderInfo.value.balance.toFixed(6)} ETH`)
    return
  }

  // 获取当前Gas价格（仅用于显示，不阻止执行）
  const gasPrice = await getCurrentGasPrice()
  currentGasPrice.value = gasPrice
  
  console.log(`当前Gas价格: ${parseFloat(gasPrice.toFixed(4))} Gwei，最大Gas价格: ${maxGasPrice.value} Gwei`)

  // 显示确认弹窗并进行预估（gas价格检查和等待在confirmAndExecute中进行）
  showConfirmDialog.value = true
  await estimateGasAndFee()
}

// 关闭确认弹窗
function closeConfirmDialog() {
  showConfirmDialog.value = false
}

// 确认并执行
async function confirmAndExecute() {
  // 最终余额检查（包含手续费）
  let totalCost
  if (useContractDisperse.value) {
    // 合约分发模式：总金额 + 预估手续费
    totalCost = Number(totalAmount.value) + Number(estimatedFee.value)
  } else {
    // 私钥分发模式：总金额 + (21000 gas * gas price * 地址数量)
    const gasPrice = await getCurrentGasPrice()
    const gasPriceWei = web3.value.utils.toWei(gasPrice.toString(), 'gwei')
    const totalGasFee = parseFloat(web3.value.utils.fromWei((BigInt('21000') * BigInt(gasPriceWei) * BigInt(addressData.value.length.toString())).toString(), 'ether'))
    totalCost = Number(totalAmount.value) + totalGasFee
  }
  
  if (senderInfo.value.balance < totalCost) {
    alert(`余额不足！总计需要 ${totalCost.toFixed(6)} ETH（包含手续费），当前余额 ${senderInfo.value.balance.toFixed(6)} ETH`)
    return
  }

  // 关闭弹窗并开始执行
  showConfirmDialog.value = false

  isRunning.value = true
  stopFlag = false
  executedCount.value = 0

  try {
    if (useContractDisperse.value) {
      // 合约分发模式：等待Gas价格合适
      while (!stopFlag) {
        // 获取当前Gas价格
        const gasPrice = await getCurrentGasPrice()
        currentGasPrice.value = gasPrice

        if (gasPrice < maxGasPrice.value && gasPrice > 0) {
          statusMessage.value = `Gas价格合适 (${parseFloat(gasPrice.toFixed(4))} Gwei)，开始执行合约批量分发...`
          
          // 执行合约批量分发
          const result = await executeDisperse()

          if (result.success) {
            executedCount.value = 1
            statusMessage.value = '合约批量分发完成'
          } else {
            statusMessage.value = '合约批量分发失败: ' + (result.error || '未知错误')
          }
          
          stopFlag = true // 完成后停止
        } else {
          statusMessage.value = `Gas价格过高 (${parseFloat(gasPrice.toFixed(4))} Gwei)，等待价格下降...`
          await sleep(3) // 等待3秒后重新检查
        }
      }
    } else {
      // 私钥分发模式：直接开始执行，不等待Gas价格
      statusMessage.value = '开始执行私钥逐笔分发...'
      
      const result = await executePrivateKeyDisperse()

      if (result.success) {
        statusMessage.value = `私钥分发完成: 成功 ${result.totalSent} 笔，失败 ${result.totalFailed} 笔`
      } else {
        statusMessage.value = '私钥分发失败: ' + (result.error || '未知错误')
      }
    }

  } catch (error) {
    console.error('执行过程中出错:', error)
    statusMessage.value = '执行过程中出错: ' + getErrorMessage(error)
  } finally {
    isRunning.value = false
  }
}

// 停止分发
function stopDisperse() {
  stopFlag = true
  statusMessage.value = '正在停止执行...'
}

// 监听私钥变化，更新发送者信息
import { watch } from 'vue'

watch(privateKey, async () => {
  if (privateKey.value.trim()) {
    // 确保Web3已初始化
    if (!web3.value) {
      initWeb3(rpcUrl.value)
    }
    // 延迟一下等待web3初始化完成
    setTimeout(async () => {
      await updateSenderInfo()
      // 如果是私钥分发模式，同时刷新nonce
      if (!useContractDisperse.value) {
        await refreshNonce()
      }
    }, 100)
  } else {
    // 私钥为空时清空发送者信息和nonce
    senderInfo.value = { address: '', balance: 0 }
    currentNonce.value = null
  }
})

// 监听来自其他页面的网络配置变化
function loadSharedNetworkConfig() {
  const contractData = JSON.parse(localStorage.getItem(STORAGE_KEYS.CONTRACT_INTERACTION) || '{}')
  if (contractData.selectedNetwork && contractData.rpcUrl) {
    // 检查是否有变化，避免无限循环
    if (selectedNetwork.value !== contractData.selectedNetwork || rpcUrl.value !== contractData.rpcUrl) {
      console.log('检测到其他页面的网络配置变化，正在同步...')
      selectedNetwork.value = contractData.selectedNetwork
      rpcUrl.value = contractData.rpcUrl
      
      // 重新初始化Web3并刷新数据
      initWeb3(rpcUrl.value)
      setTimeout(async () => {
        const gasPrice = await getCurrentGasPrice()
        if (gasPrice > 0) {
          currentGasPrice.value = gasPrice
          console.log('同步后gas价格已刷新:', gasPrice, 'Gwei')
        }
        
        // 如果有私钥，同时刷新发送者余额信息
        if (privateKey.value.trim()) {
          console.log('同步后刷新发送者余额...')
          await updateSenderInfo()
        }
      }, 500)
    }
  }
}

// 页面可见性变化时检查网络配置是否有更新
document.addEventListener('visibilitychange', () => {
  if (!document.hidden) {
    loadSharedNetworkConfig()
  }
})

// 监听localStorage变化（同一浏览器窗口内的实时同步）
window.addEventListener('storage', (e) => {
  if (e.key === STORAGE_KEYS.CONTRACT_INTERACTION) {
    loadSharedNetworkConfig()
  }
})

// 初始化Gas价格设置
async function initializeGasPrice() {
  try {
    // 确保Web3已初始化
    if (!web3.value) {
      initWeb3(rpcUrl.value)
    }
    
    // 获取实时Gas价格
    const gasPrice = await getCurrentGasPrice()
    if (gasPrice > 0) {
      const rounded = Number(gasPrice.toFixed(4))
      currentGasPrice.value = rounded
      if (maxGasPrice.value <= 0) {
        maxGasPrice.value = rounded
        console.log('初始化最大Gas价格为实时价格:', rounded, 'Gwei')
      }
    }
  } catch (error) {
    console.warn('初始化Gas价格失败:', error)
    // 如果获取失败，仅在未设置时使用默认值
    if (maxGasPrice.value <= 0) {
      maxGasPrice.value = 1.3
    }
  }
}

// 初始加载时获取最新的网络配置和Gas价格
loadSharedNetworkConfig()
initializeGasPrice()
</script>

<style scoped>
.batch-disperse {
  width: 100%;
  min-height: 100vh;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  margin: 0;
  box-sizing: border-box;
  background: transparent;
}

.container {
  width: 100%;
  margin: 0;
  padding: 20px;
  box-sizing: border-box;
}

/* 主要内容区域水平布局 */
.main-content {
  display: flex;
  gap: 25px;
  align-items: flex-start;
}

.left-panel {
  flex: 1;
  min-width: 0;
  flex-basis: 0;
}

.middle-panel {
  flex: 1;
  min-width: 0;
  flex-basis: 0;
}

.right-panel {
  flex: 1;
  min-width: 0;
  flex-basis: 0;
}





/* 执行状态网格：强制两行两列布局 */
.execution-status-grid {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  grid-template-rows: 1fr 1fr !important;
  gap: 15px !important;
}

.result-status {
  font-weight: 600;
  font-size: 14px;
}

.tx-amount {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #cccccc;
}

@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .left-panel, .middle-panel, .right-panel {
    max-width: none;
    min-width: auto;
  }
}

/* 分发模式开关样式 */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  color: #cccccc;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.switch-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #444444;
  transition: .3s;
  border-radius: 24px;
  border: 1px solid #666666;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 2px;
  bottom: 2px;
  background-color: #cccccc;
  transition: .3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #6b9bd1;
  border-color: #6b9bd1;
}

input:checked + .slider:before {
  transform: translateX(26px);
  background-color: #ffffff;
}

.switch-label {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.mode-description {
  margin-top: 8px;
}

.mode-desc {
  color: #aaaaaa;
  font-size: 12px;
  margin: 0;
  padding: 8px 12px;
  background: rgba(40, 40, 40, 0.5);
  border-radius: 6px;
  border-left: 3px solid #6b9bd1;
}

.recipient-info {
  color: #999999;
  font-size: 11px;
}

/* nonce相关样式 */
.nonce-section {
  margin-top: 15px;
}

.nonce-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.nonce-display {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nonce-value {
  color: #ffffff;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  font-weight: 600;
  padding: 6px 12px;
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid #666666;
  border-radius: 6px;
  min-width: 80px;
  text-align: center;
}

@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  /* 移动端执行状态网格改为单列 */
  .execution-status-grid {
    grid-template-columns: 1fr !important;
    grid-template-rows: repeat(4, 1fr) !important;
  }

  .switch-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* 确认弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: rgba(40, 40, 40, 0.95);
  border-radius: 16px;
  padding: 0;
  min-width: 500px;
  max-width: 90vw;
  max-height: 90vh;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(60, 60, 60, 0.8);
  overflow: hidden;
}

.modal-header {
  padding: 25px 30px 20px;
  border-bottom: 1px solid rgba(60, 60, 60, 0.5);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  color: #ffffff;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: #cccccc;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.modal-body {
  padding: 25px 30px;
}

.confirm-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(60, 60, 60, 0.3);
}

.confirm-item:last-child {
  border-bottom: none;
}

.confirm-item.total {
  border-top: 2px solid rgba(107, 155, 209, 0.5);
  margin-top: 15px;
  padding-top: 20px;
  font-weight: 600;
  font-size: 16px;
}

.confirm-item .label {
  color: #cccccc;
  font-weight: 500;
}

.confirm-item .value {
  color: #ffffff;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.confirm-item.total .value {
  color: #6b9bd1;
}

.gas-high {
  color: #ff6b6b !important;
}

.gas-normal {
  color: #51cf66 !important;
}

.modal-footer {
  padding: 20px 30px 30px;
  display: flex;
  gap: 15px;
  justify-content: flex-end;
}



@media (max-width: 768px) {
  .modal-content {
    min-width: auto;
    width: 95vw;
    margin: 10px;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 20px;
    padding-right: 20px;
  }

  .modal-footer {
    flex-direction: column;
  }

  .modal-footer .btn {
    width: 100%;
  }

  .confirm-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
    padding: 15px 0;
  }

  .confirm-item .value {
    font-size: 14px;
  }


}
</style>