# 🔐 以太坊钱包生成器使用说明

## 功能介绍

这个Web应用程序复制了你的Python脚本的所有功能，可以批量生成以太坊钱包，支持两种模式：

### 🎯 主要功能

1. **助记词生成模式** - 基于单个助记词生成多个HD钱包
2. **随机生成模式** - 每个钱包都是完全独立随机生成
3. **批量生成** - 最多支持10,000个钱包
4. **文件下载** - 生成三种格式的文件供下载
5. **实时预览** - 查看前5个生成的地址

## 🚀 如何使用

### 第一步：选择生成模式
- **助记词生成模式**：所有钱包都来自同一个助记词，使用BIP44派生路径 `m/44'/60'/0'/0/{index}`
- **随机生成模式**：每个钱包都是独立生成的

### 第二步：配置参数
- **钱包数量**：设置要生成的钱包数量（1-10000）
- **包含ETH数量**：选择是否在地址文件中包含ETH数量
- **ETH数量**：如果选择包含，设置每个地址的ETH数量

### 第三步：生成钱包
点击"🚀 生成钱包"按钮，应用将：
1. 显示进度条和实时进度
2. 根据选择的模式生成钱包
3. 完成后显示结果和统计信息

### 第四步：下载文件
生成完成后，你可以下载以下文件：

1. **📄 address.txt** - 包含所有地址的列表
   ```
   0x1234...
   0x5678...
   ```
   或者包含数量的格式：
   ```
   0x1234..., 0.000000800000000000
   0x5678..., 0.000000800000000000
   ```

2. **🔐 address-w.txt** - 包含地址和私钥
   ```
   0x1234... 0xabcd1234...
   0x5678... 0xefgh5678...
   ```

3. **🔑 mnemonic.txt** - 助记词文件（仅助记词模式）
   ```
   abandon ability able about above absent absorb abstract absurd abuse access accident
   ```

## ⚠️ 安全提醒

1. **助记词安全**：助记词可以恢复所有生成的钱包，请妥善保存
2. **私钥保护**：私钥文件包含敏感信息，下载后请安全存储
3. **本地生成**：所有钱包都在你的浏览器本地生成，不会上传到服务器
4. **离线使用**：建议在离线环境中使用以确保最高安全性

## 🔧 技术细节

- **助记词**：使用BIP39标准生成12个单词的助记词
- **HD钱包**：遵循BIP44标准，派生路径为 `m/44'/60'/0'/0/{index}`
- **以太坊兼容**：生成的钱包完全兼容以太坊和其他EVM链
- **库依赖**：使用ethers.js和bip39库确保标准兼容性

## 📊 性能参考

- **小批量（1-100个）**：几乎瞬时完成
- **中等批量（100-1000个）**：通常在1-5秒内完成
- **大批量（1000-10000个）**：可能需要10-60秒，具体取决于设备性能

## 🌐 浏览器兼容性

支持现代浏览器：
- Chrome 88+
- Firefox 78+
- Safari 14+
- Edge 88+

## 🆘 故障排除

如果遇到问题：
1. 确保浏览器支持现代JavaScript特性
2. 检查浏览器控制台是否有错误信息
3. 尝试刷新页面重新开始
4. 对于大批量生成，请耐心等待完成

## 💡 使用建议

1. **测试先行**：首次使用时建议先生成少量钱包进行测试
2. **批量处理**：对于大量钱包需求，建议分批生成
3. **备份重要**：及时备份生成的文件，特别是助记词
4. **验证正确**：可以使用MetaMask等钱包导入助记词验证正确性

---

访问 http://localhost:5173 开始使用钱包生成器！ 