<script setup>
import WalletGenerator from './components/WalletGenerator.vue'
import ContractInteraction from './components/ContractInteraction.vue'
import BatchDisperse from './components/BatchDisperse.vue'
import { ref } from 'vue'

// 当前激活的tab
const activeTab = ref('wallet')

// 切换tab
function switchTab(tab) {
  activeTab.value = tab
}
</script>

<template>
  <div id="app">
    <!-- Tab导航栏 -->
    <div class="tab-nav">
      <div class="nav-container">
        <button 
          @click="switchTab('wallet')" 
          :class="['tab-btn', { active: activeTab === 'wallet' }]"
        >
          🔐 生成钱包
        </button>
        <button 
          @click="switchTab('disperse')" 
          :class="['tab-btn', { active: activeTab === 'disperse' }]"
        >
          💸 批量分发
        </button>
        <button 
          @click="switchTab('contract')" 
          :class="['tab-btn', { active: activeTab === 'contract' }]"
        >
          🤖 合约交互
        </button>
      </div>
    </div>

    <!-- Tab内容 -->
    <div class="tab-content">
      <WalletGenerator v-if="activeTab === 'wallet'" />
      <BatchDisperse v-if="activeTab === 'disperse'" />
      <ContractInteraction v-if="activeTab === 'contract'" />
    </div>
  </div>
</template>

<style>
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%, #0a0a0a 100%);
  background-attachment: fixed;
  background-size: 100% 100%;
  color: #ffffff;
}

#app {
  width: 100vw;
  min-height: 100vh;
  background: transparent;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}

/* Tab导航栏样式 */
.tab-nav {
  background: rgba(30, 30, 30, 0.9);
  border-bottom: 1px solid rgba(60, 60, 60, 0.3);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(0,0,0,0.3);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  padding: 0 20px;
}

.tab-btn {
  background: none;
  border: none;
  padding: 20px 30px;
  font-size: 16px;
  font-weight: 500;
  color: #cccccc;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  white-space: nowrap;
}

.tab-btn:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

.tab-btn.active {
  color: #ffffff;
  border-bottom-color: #6b9bd1;
  background: rgba(255, 255, 255, 0.15);
}

/* Tab内容样式 */
.tab-content {
  flex: 1;
  width: 100%;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 15px;
  }
  
  .tab-btn {
    padding: 15px 20px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0 10px;
  }
  
  .tab-btn {
    padding: 12px 15px;
    font-size: 13px;
  }
}

/* 覆盖main.css中的媒体查询 */
@media (min-width: 1024px) {
  #app {
    display: flex !important;
    grid-template-columns: none !important;
    place-items: unset !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  body {
    display: block !important;
    place-items: unset !important;
  }
}
</style>
