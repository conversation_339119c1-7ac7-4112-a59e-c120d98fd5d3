# 可复用组件使用指南

本文档介绍项目中新创建的可复用组件，帮助您快速理解和使用这些组件。

## 📁 组件目录结构

```
src/components/
├── common/                    # 通用组件
│   ├── AddressDisplay.vue     # 地址显示 (已存在)
│   ├── FileUpload.vue         # 文件上传 (已存在)
│   ├── ProgressBar.vue        # 进度条 (已存在)
│   ├── StatusGrid.vue         # 状态网格 (已存在)
│   ├── BaseCard.vue           # 📦 卡片容器
│   ├── BaseButton.vue         # 🔘 通用按钮
│   ├── FormInput.vue          # 📝 表单输入
│   ├── ActionButtonGroup.vue  # 🎯 操作按钮组
│   ├── FormatHelp.vue         # 📋 格式帮助
│   ├── FormatItem.vue         # 📄 格式项目
│   ├── TransactionResults.vue # 📝 交易结果
│   └── ConfirmationModal.vue  # 💬 确认弹窗
├── layout/                    # 布局组件
│   └── BaseLayout.vue         # 📐 基础布局
├── network/                   # 网络相关 (已存在)
│   ├── NetworkSelector.vue
│   └── RpcTester.vue
└── wallet/                    # 钱包相关 (已存在)
    └── WalletPreview.vue
```

## 🚀 组件使用指南

### 1. BaseLayout - 基础布局

**功能:** 提供统一的三栏、两栏或单栏页面布局

```vue
<template>
  <!-- 三栏布局 (默认) -->
  <BaseLayout>
    <template #left>
      <BaseCard title="配置">
        <!-- 左侧内容 -->
      </BaseCard>
    </template>
    
    <template #middle>
      <BaseCard title="操作">
        <!-- 中间内容 -->
      </BaseCard>
    </template>
    
    <template #right>
      <BaseCard title="状态">
        <!-- 右侧内容 -->
      </BaseCard>
    </template>
  </BaseLayout>

  <!-- 两栏布局 -->
  <BaseLayout two-column>
    <template #left><!-- 左侧内容 --></template>
    <template #right><!-- 右侧内容 --></template>
  </BaseLayout>

  <!-- 单栏布局 -->
  <BaseLayout single-column>
    <div class="single-content">
      <!-- 单栏内容 -->
    </div>
  </BaseLayout>
</template>
```

**属性:**
- `layoutClass` - 自定义CSS类
- `singleColumn` - 单栏模式
- `twoColumn` - 两栏模式

### 2. BaseCard - 卡片容器

**功能:** 统一的卡片容器，支持标题、操作按钮等

```vue
<template>
  <!-- 基础卡片 -->
  <BaseCard title="网络配置" variant="config">
    <p>卡片内容</p>
  </BaseCard>

  <!-- 带操作按钮的卡片 -->
  <BaseCard title="交易记录">
    <template #actions>
      <BaseButton size="small" variant="danger">清除</BaseButton>
    </template>
    <p>卡片内容</p>
  </BaseCard>

  <!-- 自定义头部的卡片 -->
  <BaseCard>
    <template #header>
      <h3>自定义标题</h3>
      <span class="status">运行中</span>
    </template>
    <p>卡片内容</p>
  </BaseCard>
</template>
```

**属性:**
- `title` - 卡片标题
- `variant` - 样式变体 (`default`, `config`, `status`, `result`)
- `customClass` - 自定义CSS类

### 3. BaseButton - 通用按钮

**功能:** 统一的按钮组件，支持多种样式和状态

```vue
<template>
  <!-- 基础按钮 -->
  <BaseButton @click="handleClick">点击我</BaseButton>

  <!-- 不同变体的按钮 -->
  <BaseButton variant="success">🚀 开始执行</BaseButton>
  <BaseButton variant="danger">⏹️ 停止</BaseButton>
  <BaseButton variant="secondary">取消</BaseButton>

  <!-- 不同尺寸的按钮 -->
  <BaseButton size="small">小按钮</BaseButton>
  <BaseButton size="large">大按钮</BaseButton>

  <!-- 加载状态按钮 -->
  <BaseButton :loading="isLoading" loading-text="处理中...">
    提交
  </BaseButton>

  <!-- 块级按钮 -->
  <BaseButton block>全宽按钮</BaseButton>

  <!-- 轮廓按钮 -->
  <BaseButton variant="primary" outlined>轮廓按钮</BaseButton>
</template>
```

**属性:**
- `variant` - 按钮变体 (`primary`, `secondary`, `success`, `danger`, `warning`, `info`)
- `size` - 按钮尺寸 (`small`, `medium`, `large`)
- `disabled` - 禁用状态
- `loading` - 加载状态
- `loadingText` - 加载时显示的文本
- `block` - 块级按钮
- `outlined` - 轮廓样式

### 4. FormInput - 表单输入

**功能:** 统一的表单输入组件，支持多种输入类型

```vue
<template>
  <!-- 文本输入 -->
  <FormInput 
    v-model="name"
    label="用户名"
    placeholder="请输入用户名"
    required
  />

  <!-- 数字输入 -->
  <FormInput 
    v-model="amount"
    label="金额"
    type="number"
    :min="0"
    :step="0.001"
    wide
  />

  <!-- 选择框 -->
  <FormInput 
    v-model="network"
    label="网络"
    type="select"
    :options="networkOptions"
    placeholder="选择网络"
  />

  <!-- 文本域 -->
  <FormInput 
    v-model="description"
    label="描述"
    type="textarea"
    :rows="4"
    description="可选的详细描述"
  />

  <!-- 垂直布局 -->
  <FormInput 
    v-model="config"
    label="配置"
    vertical
    description="垂直排列的输入框"
  />

  <!-- 带错误信息 -->
  <FormInput 
    v-model="email"
    label="邮箱"
    type="email"
    :error="emailError"
  />
</template>

<script setup>
const networkOptions = [
  { label: '以太坊主网', value: 'mainnet' },
  { label: '以太坊测试网', value: 'sepolia' }
]
</script>
```

**属性:**
- `modelValue` - 双向绑定值
- `label` - 输入框标签
- `type` - 输入类型 (`text`, `number`, `email`, `password`, `select`, `textarea`)
- `placeholder` - 占位符
- `disabled` - 禁用状态
- `required` - 必填标记
- `vertical` - 垂直布局
- `wide` - 宽输入框
- `options` - 选择框选项 (type="select"时使用)
- `description` - 描述文本
- `error` - 错误信息

### 5. ActionButtonGroup - 操作按钮组

**功能:** 统一的操作按钮区域

```vue
<template>
  <ActionButtonGroup>
    <template #buttons>
      <BaseButton variant="success" :disabled="isRunning">
        🚀 开始执行
      </BaseButton>
      <BaseButton variant="danger" :disabled="!isRunning">
        ⏹️ 停止执行
      </BaseButton>
      <BaseButton variant="danger">
        🗑️ 清除数据
      </BaseButton>
    </template>
    
    <template #tips>
      <p>执行前请确保网络连接正常</p>
    </template>
  </ActionButtonGroup>

  <!-- 垂直布局 -->
  <ActionButtonGroup vertical :auto-save-tip="false">
    <template #buttons>
      <!-- 按钮内容 -->
    </template>
  </ActionButtonGroup>
</template>
```

**属性:**
- `autoSaveTip` - 显示自动保存提示 (默认: true)
- `vertical` - 垂直布局

### 6. FormatHelp - 格式帮助

**功能:** 显示文件格式帮助信息

```vue
<template>
  <FormatHelp title="📋 支持的文件格式">
    <FormatItem 
      title="格式1: 地址 + 私钥 (空格分隔)"
      :examples="[
        '0x1234...abcd 0xabcd1234...5678',
        '0x5678...efgh 0xefgh5678...9abc'
      ]"
    />
    
    <FormatItem 
      title="格式2: 仅私钥 (每行一个)"
      :examples="[
        '0xabcd1234...5678',
        '0xefgh5678...9abc'
      ]"
      description="地址将自动从私钥推导生成"
    />
  </FormatHelp>
</template>
```

**FormatHelp 属性:**
- `title` - 帮助标题

**FormatItem 属性:**
- `title` - 格式标题
- `examples` - 示例数组
- `description` - 描述文本

### 7. TransactionResults - 交易结果

**功能:** 显示交易结果列表

```vue
<template>
  <TransactionResults 
    :results="transactionResults"
    title="📝 执行记录"
    :max-display="50"
    @clear="clearResults"
  >
    <!-- 自定义结果信息显示 -->
    <template #result-info="{ result }">
      <AddressDisplay :address="result.address" />
      <span class="amount">{{ result.amount }} ETH</span>
      <span class="status">{{ result.success ? '✅' : '❌' }}</span>
    </template>
    
    <!-- 自定义结果详情显示 -->
    <template #result-details="{ result }">
      <div v-if="result.gasUsed">Gas使用: {{ result.gasUsed }}</div>
      <div v-if="result.txHash">哈希: {{ result.txHash }}</div>
    </template>
  </TransactionResults>
</template>
```

**属性:**
- `results` - 结果数组
- `title` - 标题
- `maxDisplay` - 最大显示数量

**事件:**
- `clear` - 清除记录

### 8. ConfirmationModal - 确认弹窗

**功能:** 统一的确认对话框

```vue
<template>
  <ConfirmationModal
    :show="showConfirm"
    title="📋 批量分发确认"
    :items="confirmItems"
    :loading="isProcessing"
    loading-text="执行中..."
    @close="closeModal"
    @confirm="handleConfirm"
  />

  <!-- 自定义内容的弹窗 -->
  <ConfirmationModal
    :show="showCustom"
    title="自定义确认"
    @close="closeModal"
    @confirm="handleConfirm"
  >
    <div class="custom-content">
      <p>这是自定义的弹窗内容</p>
      <ul>
        <li>项目1</li>
        <li>项目2</li>
      </ul>
    </div>
  </ConfirmationModal>
</template>

<script setup>
const confirmItems = [
  { label: '分发地址数量', value: '100 个地址' },
  { label: '分发总额', value: '0.1 ETH' },
  { label: '预估手续费', value: '0.001 ETH' },
  { label: '总计花费', value: '0.101 ETH', isTotal: true }
]
</script>
```

**属性:**
- `show` - 显示状态
- `title` - 弹窗标题
- `items` - 确认项目数组
- `cancelText` - 取消按钮文本
- `confirmText` - 确认按钮文本
- `loading` - 加载状态
- `loadingText` - 加载文本

**事件:**
- `close` - 关闭弹窗
- `confirm` - 确认操作

## 🎯 使用示例

### 完整页面示例

```vue
<template>
  <BaseLayout layout-class="batch-disperse">
    <!-- 左侧配置面板 -->
    <template #left>
      <BaseCard title="网络配置" variant="config">
        <NetworkSelector 
          v-model="rpcUrl"
          :default-network="selectedNetwork"
          @network-change="handleNetworkChange"
        />
        <RpcTester :rpc-url="rpcUrl" />
      </BaseCard>

      <BaseCard title="批量分发配置" variant="config">
        <FormInput 
          v-model="privateKey"
          label="发送者私钥"
          type="password"
          placeholder="输入私钥"
          wide
        />
        
        <FormInput 
          v-model="contractAddress"
          label="Disperse合约地址"
          placeholder="0x..."
          wide
        />
      </BaseCard>
    </template>

    <!-- 中间数据面板 -->
    <template #middle>
      <BaseCard title="地址数据" variant="config">
        <FormatHelp>
          <FormatItem 
            title="格式: 地址,金额 (逗号分隔)"
            :examples="['0x1234...abcd,0.001', '0x5678...efgh,0.002']"
            description="每行一个地址和对应的ETH金额"
          />
        </FormatHelp>
        
        <FileUpload 
          accepted-types=".txt"
          description="支持 .txt 文件格式 (地址,金额)"
          @file-selected="handleFileSelected"
        />
        
        <ActionButtonGroup>
          <template #buttons>
            <BaseButton 
              variant="success" 
              :disabled="isRunning || addressData.length === 0"
              @click="startDisperse"
            >
              <span v-if="isRunning">执行中...</span>
              <span v-else>🚀 开始批量分发</span>
            </BaseButton>
            
            <BaseButton 
              variant="danger" 
              :disabled="!isRunning"
              @click="stopDisperse"
            >
              ⏹️ 停止执行
            </BaseButton>
          </template>
        </ActionButtonGroup>
      </BaseCard>
    </template>

    <!-- 右侧状态面板 -->
    <template #right>
      <BaseCard v-if="isRunning" title="📊 执行状态" variant="status">
        <StatusGrid :items="executionStatusItems" />
        <ProgressBar :current="executedCount" :total="1" />
      </BaseCard>

      <TransactionResults 
        :results="transactionResults"
        @clear="clearTransactionResults"
      />
    </template>
  </BaseLayout>

  <!-- 确认弹窗 -->
  <ConfirmationModal
    :show="showConfirmDialog"
    title="📋 批量分发确认"
    :items="confirmItems"
    :loading="isEstimating"
    loading-text="预估中..."
    @close="closeConfirmDialog"
    @confirm="confirmAndExecute"
  />
</template>
```

## 🔧 迁移指南

### 从旧代码迁移到新组件

#### 1. 替换卡片容器

**旧代码:**
```vue
<div class="config-panel">
  <div class="config-section">
    <h3>网络配置</h3>
    <!-- 内容 -->
  </div>
</div>
```

**新代码:**
```vue
<BaseCard title="网络配置" variant="config">
  <!-- 内容 -->
</BaseCard>
```

#### 2. 替换按钮

**旧代码:**
```vue
<button 
  @click="startInteraction" 
  :disabled="isRunning" 
  class="btn btn-success"
>
  <span v-if="isRunning">执行中...</span>
  <span v-else>🚀 开始执行</span>
</button>
```

**新代码:**
```vue
<BaseButton 
  variant="success"
  :loading="isRunning"
  loading-text="执行中..."
  @click="startInteraction"
>
  🚀 开始执行
</BaseButton>
```

#### 3. 替换输入框

**旧代码:**
```vue
<div class="input-group">
  <label>目标地址:</label>
  <input 
    type="text" 
    v-model="targetAddress" 
    placeholder="0x..."
    class="wide-input"
  />
</div>
```

**新代码:**
```vue
<FormInput 
  v-model="targetAddress"
  label="目标地址"
  placeholder="0x..."
  wide
/>
```

## 💡 最佳实践

1. **组件组合:** 优先使用组件组合而不是继承
2. **插槽使用:** 充分利用插槽的灵活性
3. **属性验证:** 使用属性验证确保组件的正确使用
4. **事件命名:** 使用清晰的事件命名约定
5. **样式隔离:** 使用scoped样式避免样式污染
6. **响应式设计:** 所有组件都支持响应式布局

## 🐛 故障排除

### 常见问题

1. **组件不显示**
   - 检查组件是否正确导入
   - 确认必需的属性是否已传递

2. **样式不生效**
   - 确认组件使用了scoped样式
   - 检查CSS变量是否正确定义

3. **事件不触发**
   - 确认事件名称是否正确
   - 检查父组件是否正确监听事件

---

通过使用这些可复用组件，您可以：
- 🚀 **提高开发效率** - 减少重复代码编写
- 🎨 **保持UI一致性** - 统一的设计语言
- 🔧 **降低维护成本** - 集中管理样式和逻辑
- 📱 **响应式支持** - 自动适配不同屏幕尺寸