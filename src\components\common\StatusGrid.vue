<template>
  <div class="status-grid">
    <div 
      v-for="(item, index) in items" 
      :key="index" 
      class="status-item"
    >
      <span class="status-label">{{ item.label }}:</span>
      <span 
        class="status-value" 
        :class="getStatusClass(item.status)"
      >
        {{ item.value }}
      </span>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  items: {
    type: Array,
    default: () => []
  }
})

function getStatusClass(status) {
  const classes = []
  
  if (status === 'success') classes.push('success')
  else if (status === 'error') classes.push('error')
  else if (status === 'warning') classes.push('warning')
  else if (status === 'loading') classes.push('loading')
  else if (status === 'gas-high') classes.push('gas-high')
  
  return classes
}
</script>

<style scoped>
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.status-item {
  background: rgba(60, 60, 60, 0.6);
  padding: 15px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  color: #cccccc;
  font-weight: 500;
}

.status-value {
  font-weight: 600;
  color: #ffffff;
}

.status-value.success {
  color: #27ae60;
}

.status-value.error {
  color: #e53e3e;
}

.status-value.warning {
  color: #d68910;
}

.status-value.gas-high {
  color: #e53e3e;
}

.status-value.loading {
  color: #d68910;
}

@media (max-width: 768px) {
  .status-grid {
    grid-template-columns: 1fr;
  }
}
</style> 